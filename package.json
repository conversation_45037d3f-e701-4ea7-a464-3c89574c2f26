{"name": "dojo", "private": true, "version": "0.0.28", "config": {"timestamp": "1753287594"}, "type": "module", "scripts": {"proxy": "lcp --proxyUrl https://acsdev.iointel.com &", "dev_proxy": "yarn proxy && vite --mode dev_proxy", "dev": "vite --mode development", "tsc": "tsc", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:unit": "vitest --root src/", "coverage": "vitest --coverage"}, "dependencies": {"@emotion/react": "11.14.0", "@emotion/styled": "11.14.1", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.2", "@headlessui/react": "^1.7.3", "@heroicons/react": "^2.1.1", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.1", "@mui/x-data-grid-pro": "^8.1.0", "@tailwindcss/forms": "^0.5.3", "@tanstack/react-query": "^4.20.4", "@types/array-move": "^2.0.0", "@types/dagre": "^0.7.48", "@types/node": "^18.11.17", "@types/prismjs": "^1.26.0", "@types/react-sortable-hoc": "^0.7.1", "@types/sanitize-html": "^2.9.0", "antd": "^5.26.3", "apexcharts": "^3.41.0", "array-move": "^4.0.0", "axios": "^1.2.1", "buffer": "^6.0.3", "casual": "^1.6.2", "chance": "^1.1.9", "chart.js": "^4.4.0", "chartjs-plugin-zoom": "^2.0.1", "classnames": "^2.3.2", "dagre": "^0.8.5", "esbuild": "^0.17.14", "form-data": "^4.0.0", "highlight.js": "^11.7.0", "js-cookie": "^3.0.1", "jwt-decode": "^3.1.2", "keycloak-js": "^23.0.6", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "prismjs": "^1.29.0", "purify-ts": "^2.0.3", "react": "^18.2.0", "react-apexcharts": "^1.4.1", "react-chartjs-2": "^5.0.0", "react-contenteditable": "^3.3.7", "react-data-table-component": "^7.6.2", "react-datepicker": "^4.8.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-js-cron": "5.2.0", "react-json-inspector": "^7.1.1", "react-loader-spinner": "^6.1.6", "react-router-dom": "^6.4.3", "react-select": "^5.8.0", "react-simple-code-editor": "^0.13.1", "react-sortable": "^2.0.0", "react-sortable-hoc": "^2.0.0", "react-use-event-hook": "^0.9.3", "reactflow": "^11.4.0", "reactjs-toggleswitch": "^1.4.0", "recharts": "^2.7.2", "sanitize-html": "^2.10.0", "sass": "^1.60.0", "sort-by": "^0.0.2", "styled-components": "^6.1.11", "swiper": "^10.3.1", "use-query-params": "^2.2.1", "usehooks-ts": "^2.9.1", "uuid": "^9.0.0", "yup": "^0.32.11"}, "resolutions": {"styled-components": "^5"}, "devDependencies": {"@testing-library/jest-dom": "^6.2.1", "@testing-library/react": "^14.1.2", "@types/axios": "^0.14.0", "@types/chance": "^1.1.3", "@types/js-cookie": "^3.0.2", "@types/jwt-decode": "^3.1.0", "@types/localforage": "^0.0.34", "@types/match-sorter": "^6.0.0", "@types/react": "^18.2.15", "@types/react-datepicker": "^4.8.0", "@types/react-dom": "^18.0.6", "@types/react-dropzone": "^5.1.0", "@types/react-router-dom": "^5.3.3", "@types/uuid": "^9.0.0", "@vitejs/plugin-react": "^2.1.0", "autoprefixer": "^10.4.12", "esbuild": "^0.17.14", "jsdom": "^24.0.0", "postcss": "^8.4.18", "tailwindcss": "^3.4.4", "typescript": "^5.3.3", "vite": "^3.1.0", "vitest": "^1.2.1"}}