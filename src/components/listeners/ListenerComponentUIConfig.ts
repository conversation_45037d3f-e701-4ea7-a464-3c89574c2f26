// ListenerComponentUIConfig.ts

export interface ComponentUIConfig {
  compInfo: {
    enabled: boolean;
    label: string;
  };
  protocol: {
    enabled: boolean;
    label: string;
  };
  host: {
    enabled: boolean;
    label: string;
  };
  port: {
    enabled: boolean;
    label: string;
  };
  hostname: {
    enabled: boolean;
    label: string;
  };
  identifier?: {
    enabled: boolean;
    label: string;
  };
  connectionId?: {
    enabled: boolean;
    label: string;
  };
  basicAuth?: {
    enabled: boolean;
    label: string;
  };
  basicAuthSwitch?: {
    enabled: boolean;
    label: string;
  };
  conProps?: {
    enabled: boolean;
    label: string;
  };
  ipAddresses?: {
    enabled: boolean;
    label: string;
  };
  version?: {
    enabled: boolean;
    label: string;
  };
}

// Kafka Configuration
export const kafkaUIConfig: ComponentUIConfig = {
  compInfo: {
    enabled: true,
    label: 'Component Info',
  },
  protocol: {
    enabled: true,
    label: 'Protocol',
  },
  host: {
    enabled: true,
    label: 'Host',
  },
  port: {
    enabled: true,
    label: 'Port',
  },
  hostname: {
    enabled: true,
    label: 'Hostname',
  },
  identifier: {
    enabled: true,
    label: 'Topic',
  },
  connectionId: {
    enabled: true,
    label: 'GroupId',
  },
  basicAuthSwitch: {
    enabled: true,
    label: 'Enable Basic Authentication',
  },
  basicAuth: {
    enabled: true,
    label: 'Basic Authentication',
  },
  conProps: {
    enabled: true,
    label: 'Connection Properties',
  },
};

// MQTT Configuration
export const mqttUIConfig: ComponentUIConfig = {
  compInfo: {
    enabled: true,
    label: 'Component Info',
  },
  protocol: {
    enabled: true,
    label: 'Protocol',
  },
  host: {
    enabled: true,
    label: 'Host',
  },
  port: {
    enabled: true,
    label: 'Port',
  },
  hostname: {
    enabled: true,
    label: 'Hostname',
  },
  identifier: {
    enabled: true,
    label: 'Topic',
  },
  connectionId: {
    enabled: true,
    label: 'ClientId',
  },
  basicAuthSwitch: {
    enabled: true,
    label: 'Enable Basic Authentication',
  },
  basicAuth: {
    enabled: true,
    label: 'Basic Authentication',
  },
  conProps: {
    enabled: true,
    label: 'Connection Properties',
  },
  version: {
    enabled: true,
    label: 'Version',
  },
};

// HTTP Configuration
export const httpUIConfig: ComponentUIConfig = {
  compInfo: {
    enabled: true,
    label: 'Component Info',
  },
  protocol: {
    enabled: true,
    label: 'Protocol',
  },
  host: {
    enabled: true,
    label: 'Host',
  },
  port: {
    enabled: true,
    label: 'Port',
  },
  hostname: {
    enabled: true,
    label: 'Hostname',
  },
  identifier: {
    enabled: true,
    label: 'Path',
  },
  basicAuthSwitch: {
    enabled: true,
    label: 'Enable Basic Authentication',
  },
  basicAuth: {
    enabled: true,
    label: 'Basic Authentication',
  },
  conProps: {
    enabled: true,
    label: 'HTTP Headers',
  },
};

// TCP/UDP Configuration
export const tcpUdpUIConfig: ComponentUIConfig = {
  compInfo: {
    enabled: true,
    label: 'Component Info',
  },
  protocol: {
    enabled: true,
    label: 'Protocol',
  },
  host: {
    enabled: true,
    label: 'Host',
  },
  port: {
    enabled: true,
    label: 'Port',
  },
  hostname: {
    enabled: true,
    label: 'Hostname',
  },
  ipAddresses: {
    enabled: true,
    label: 'IP Addresses',
  },
};
