import { useFlagcard } from '@/contexts/flagcard-context';

import {
  createListener,
  editListener,
  getListenerComponents,
  getListeners,
  getListenerTypes,
} from '@/services/dojo-api-gateway/listener-service/listener';

import { decodedAccessToken } from '@/services/dojo-api-gateway/auth-service/auth-utils';
import { eventTemplate } from '@/services/dojo-api-gateway/story-service/event-template';
import { isJsonStringValidObject } from '@/utils/common';
import { Logger } from '@/utils/Logger';
import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useMemo, useState } from 'react';
import useEvent from 'react-use-event-hook';
import { useLoader } from '../loaderIndicator/LoaderContext';
import { Utils } from './ListenerUtils';
import Switch from '../switch/Switch';
import {
  ComponentUIConfig,
  httpUIConfig,
  kafkaUIConfig,
  mqttUIConfig,
  tcpUdpUIConfig,
} from './ListenerComponentUIConfig';

export const ListenerCreateEdit: React.FC<{
  createMode: Boolean;
  listener?: ListenerConfig | null;
  callback: (currentListener?: ListenerConfig) => void;
  close: () => void;
}> = ({ createMode, listener, callback, close }) => {
  const flagcard = useFlagcard();
  const { showLoader, hideLoader } = useLoader();

  let decodedUser = decodedAccessToken();
  let user = decodedUser;
  user.firstName = user.given_name;
  user.lastName = user.family_name;
  user.username = user.preferred_username;
  Logger.debug(listener);

  const isEditInternal = () => {
    return !createMode && listener?.type !== 'EXTERNAL';
  };

  const getUICfg = (param: string) => {
    const a = param as keyof ComponentUIConfig;
    if (compUiConfig) {
      return compUiConfig[a];
    }
    return false;
  };

  const portMin = 0;
  const portMax = 65535;

  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [canSave, setCanSave] = useState<boolean>(false);

  const [compUiConfig, setCompUiConfig] = useState<ComponentUIConfig>();

  const [currentListener, setCurrentListener] = useState<ListenerConfig>(
    listener ?? {
      name: '',
      description: '',
      type: '',
      user: user.username,
      eventId: '',
      component: '',
      secure: null,
      componentSpecificProps: {},
      componentSpecificConnProps: {},
      authenticationInfo: null,
      connectionInfo: null,
      ipAddresses: null,
    }
  );

  useEffect(() => {
    const hasChanges =
      JSON.stringify(currentListener) !== JSON.stringify(listener);
    setCanSave(hasChanges);
  }, [currentListener]);

  useEffect(() => {
    if (!createMode && listener != null) {
      if (
        listener.connectionInfo?.componentInfo &&
        listener.connectionInfo?.componentInfo?.topic
      ) {
        listener.connectionInfo.componentInfo.identifier =
          listener.connectionInfo.componentInfo.topic;
      }

      if (
        listener.connectionInfo?.componentInfo &&
        listener.connectionInfo?.componentInfo?.path
      ) {
        listener.connectionInfo.componentInfo.identifier =
          listener.connectionInfo.componentInfo.path;
      }

      listener.componentSpecificConnProps = {};
      if (
        listener &&
        listener.connectionInfo?.externalAccessInfo?.connectionProperties !==
          null
      ) {
        listener.componentSpecificConnProps =
          listener.connectionInfo?.externalAccessInfo?.connectionProperties;
      }

      // setCurrentListener(listener);
      currentListener.secure = listener.connectionInfo?.componentInfo?.secure;
      currentListener.host = listener.connectionInfo?.componentInfo?.host;
      currentListener.port = listener.connectionInfo?.componentInfo?.port;
      currentListener.component =
        listener.connectionInfo?.componentInfo?.component;

      currentListener.componentSpecificProps = {};
      currentListener.componentSpecificProps.identifier =
        listener.connectionInfo?.componentInfo?.identifier;

      let component = listener.connectionInfo?.componentInfo?.component;

      setCompUiConfig(() => {
        if (component === 'KAFKA') {
          return kafkaUIConfig;
        } else if (component === 'MQTT') {
          return mqttUIConfig;
        } else if (component === 'HTTP') {
          return httpUIConfig;
        } else if (component === 'TCP' || component === 'UDP') {
          return tcpUdpUIConfig;
        }
      });

      if (component === 'KAFKA') {
        setCompUiConfig(kafkaUIConfig);
        currentListener.componentSpecificProps.identifier =
          currentListener.connectionInfo?.componentInfo?.topic;
      } else if (component === 'MQTT') {
        setCompUiConfig(mqttUIConfig);
        currentListener.componentSpecificProps.identifier =
          currentListener.connectionInfo?.componentInfo?.topic;
      } else if (component === 'HTTP') {
        setCompUiConfig(httpUIConfig);
        currentListener.componentSpecificProps.identifier =
          currentListener.connectionInfo?.componentInfo?.path;
      } else if (component === 'TCP' || component === 'UDP') {
        setCompUiConfig(tcpUdpUIConfig);
      }

      if (listener.connectionInfo?.componentInfo?.connectionId) {
        currentListener.componentSpecificProps.connectionId =
          listener.connectionInfo?.componentInfo?.connectionId;
      }

      currentListener.ipAddresses = listener.connectionInfo?.ipAddresses;

      currentListener.authenticationInfo =
        listener.connectionInfo?.authenticationInfo;

      currentListener.componentSpecificConnProps =
        listener.componentSpecificConnProps;

      currentListener.componentSpecificConnPropsJson = JSON.stringify(
        currentListener.componentSpecificConnProps,
        null,
        2
      );

      setCurrentListener(listener);
    }
  }, []);

  useEffect(() => {
    const validateJson = () => {
      if (currentListener?.componentSpecificConnPropsJson) {
        const isValid = isJsonStringValidObject(
          currentListener.componentSpecificConnPropsJson
        );
        setIsFormValid(isValid);
      } else {
        setIsFormValid(false);
      }
    };

    validateJson();
  }, [currentListener.componentSpecificConnPropsJson]);

  const { data: listenerEvents } = useQuery(
    ['listenerCreateFetchEvents'],
    async () => {
      showLoader();
      const response = await eventTemplate.list();
      hideLoader();
      return response.data?.Data.content;
    },
    {
      onError: (error: any) => {
        flagcard.appear({
          type: 'error',
          message: error.message,
        });
        hideLoader();
      },
      // refetchOnMount: createMode ? true : false,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      enabled: true,
    }
  );

  const { data: listenerTypes } = useQuery(
    ['listenerCreateFetchTypes'],
    async () => {
      showLoader();
      const response = await getListenerTypes();
      hideLoader();
      return response.data?.Data.content;
    },
    {
      onError: (error: any) => {
        flagcard.appear({
          type: 'error',
          message: error.message,
        });
        hideLoader();
      },
      refetchOnMount: createMode ? true : false,
      refetchOnWindowFocus: false,
      enabled: true,
    }
  );

  const { data: listenerComponents, refetch: listenerComponentsRefetch } =
    useQuery(
      [
        'listenerCreateFetchComponents',
        currentListener.type,
        currentListener.secure,
      ],
      async () => {
        showLoader();
        const response = await getListenerComponents(
          currentListener.type,
          currentListener.secure != null ? currentListener.secure : false
        );
        hideLoader();
        setCurrentListener({
          ...currentListener,
          component: '',
        });

        return response.data?.Data.content;
      },
      {
        onError: (error: any) => {
          flagcard.appear({
            type: 'error',
            message: error.message,
          });
          hideLoader();
        },
        refetchOnMount: false,
        refetchOnWindowFocus: false,
        enabled:
          createMode &&
          currentListener.type !== '' &&
          currentListener.secure !== null,
      }
    );

  const { data: listeners, refetch: listenersRefetch } = useQuery(
    ['listenerViewerFetchListeners'],
    async () => {
      //showLoader();
      const response = await getListeners();
      Logger.debug(response);
      //hideLoader();
      return response.Data.content;
    },
    {
      onError: (error: any) => {
        flagcard.appear({
          type: 'error',
          message: error.message,
        });
        hideLoader();
      },
      refetchOnMount: createMode ? true : false,
      refetchOnWindowFocus: false,
      enabled: true,
    }
  );

  const onCreateClick = useEvent(() => {
    let result: any;
    showLoader();
    Logger.debug(currentListener);

    if (currentListener?.authenticationInfo?.type == null) {
      currentListener.authenticationInfo = null;
    }

    let createdListener: any;
    createListener(currentListener)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        createdListener = response.data.Data.content;
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        if (createdListener != null) {
          listenersRefetch().then(() => {
            if (
              createdListener.type === 'EXTERNAL' &&
              createdListener?.connectionInfo?.componentInfo?.secure
            ) {
              callback(createdListener);
            } else {
              close();
            }
          });
        }
      });
  });

  const onSaveClick = useEvent(() => {
    let result: any;
    Logger.debug(currentListener);
    showLoader();

    const listenerToUpdate = { ...currentListener };
    if (listenerToUpdate?.authenticationInfo?.type == null) {
      listenerToUpdate.authenticationInfo = null;
    }

    let updatedListener: any;
    editListener(listenerToUpdate)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        updatedListener = response.data.Data.content;

        const connInfo = updatedListener.connectionInfo;
        const compInfo = connInfo?.componentInfo;

        if (compInfo && compInfo?.topic) {
          compInfo.identifier = connInfo.componentInfo.topic;
        }

        if (updatedListener) {
          updatedListener.componentSpecificConnPropsJson = JSON.stringify(
            connInfo?.externalAccessInfo?.connectionProperties,
            null,
            2
          );
        }

        updatedListener.host = compInfo?.host;
        updatedListener.port = compInfo?.port;

        updatedListener.componentSpecificProps = {};
        updatedListener.componentSpecificProps.identifier =
          compInfo?.identifier;

        if (
          updatedListener.component === 'KAFKA' ||
          updatedListener.component === 'MQTT'
        ) {
          updatedListener.componentSpecificProps.identifier = compInfo?.topic;
        } else if (updatedListener.component === 'HTTP') {
          updatedListener.componentSpecificProps.identifier = compInfo?.path;
        }

        if (updatedListener.connectionInfo?.componentInfo?.connectionId) {
          updatedListener.componentSpecificProps.connectionId =
            updatedListener.connectionInfo?.componentInfo?.connectionId;
        }

        updatedListener.authenticationInfo = connInfo?.authenticationInfo;

        updatedListener.componentSpecificConnProps =
          connInfo?.externalAccessInfo?.connectionProperties;

        updatedListener.ipAddresses = connInfo?.ipAddresses;

        updatedListener.componentSpecificConnProps = {};
        if (
          updatedListener &&
          updatedListener.connectionInfo?.externalAccessInfo
            ?.connectionProperties !== null
        ) {
          updatedListener.componentSpecificConnProps =
            updatedListener.connectionInfo?.externalAccessInfo?.connectionProperties;
        }

        updatedListener.componentSpecificConnPropsJson = JSON.stringify(
          updatedListener.componentSpecificConnProps,
          null,
          2
        );

        setCurrentListener(updatedListener);

        if (listener) {
          callback(updatedListener);
        }
        Logger.debug(updatedListener);
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        if (updatedListener != null) {
          listenersRefetch().then(() => {
            // if (
            //   updatedListener.type === 'EXTERNAL' &&
            //   updatedListener?.connectionInfo?.componentInfo?.secure
            // ) {
            //   callback(updatedListener);
            // } else {
            //   close();
            // }
          });
        }
      });
  });

  const onNextClick = useEvent(() => {
    // save required ?
    callback(currentListener);
  });

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="w-[420px] min-h-[580px] max-h-[680px]  bg-[#ededed] shadow-lg rounded-xl  flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center border-gray-300 p-4">
          <h3 className="text-lg font-medium">
            {createMode ? 'New Listener' : currentListener.name}
          </h3>
          <button
            className="text-gray-600 hover:text-gray-800"
            onClick={() => {
              close();
            }}
            aria-label="Close"
          >
            &times;
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-scroll">
          <div style={{ display: 'block' }}>
            {/* Section 1 -> ListenerName | Description | Event */}
            <div className="px-5 pb-5">
              <div className="bg-[#E3E4E9] rounded-xl p-3">
                <div className="flex flex-col gap-2 items-center w-full z-10 overflow-x-auto shadow-none">
                  {/* Listener Name */}
                  <div className="flex flex-col w-full text-black rounded-xl">
                    <label className="block text-sm font-medium">
                      Listener Name
                    </label>
                    <input
                      className="w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
                      name="name"
                      placeholder="listener1"
                      autoComplete="off"
                      defaultValue={currentListener?.name}
                      onChange={(e) => {
                        setCurrentListener({
                          ...currentListener,
                          name: e.currentTarget.value,
                        });
                      }}
                    />
                  </div>

                  {/* Description */}
                  <div className="flex flex-col w-full text-black rounded-xl">
                    <label className="block text-sm font-medium">
                      Description
                    </label>
                    <textarea
                      className="w-full h-16 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm resize-none"
                      name="description"
                      autoComplete="off"
                      placeholder="Max 50 letters"
                      maxLength={50}
                      defaultValue={currentListener?.description}
                      onChange={(e) => {
                        setCurrentListener({
                          ...currentListener,
                          description: e.currentTarget.value,
                        });
                      }}
                    />
                  </div>

                  {/* Event */}
                  <div className="flex flex-col w-full text-black rounded-xl">
                    <label className="block text-sm font-medium">Event</label>
                    <select
                      className="block w-full pl-2 pr-10 py-2 text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md"
                      value={currentListener?.eventId ?? ''}
                      onChange={(e) => {
                        setCurrentListener({
                          ...currentListener,
                          eventId: e.currentTarget.value,
                        });
                      }}
                    >
                      <option value="" disabled>
                        Select an event
                      </option>
                      {listenerEvents &&
                        listenerEvents.map((event) => (
                          <option key={event.eventId} value={event.eventId}>
                            {event.eventName}
                          </option>
                        ))}
                    </select>
                  </div>
                </div>
              </div>
            </div>

            {/* Section 2 -> Source | Security | Protocol */}
            <div className="px-5 pb-5">
              <div className="bg-[#E3E4E9] rounded-xl p-3">
                <div className="flex flex-col gap-3 w-full z-10 overflow-x-auto shadow-none">
                  {/* Info Section */}
                  <div className="flex flex-col w-full text-[#595B61] text-center text-sm font-light">
                    This section cannot be changed once the listener has been
                    created.
                  </div>

                  {/* Source */}
                  <div className="flex flex-col w-full text-black rounded-xl">
                    <label className="block text-sm font-medium">Source</label>
                    <select
                      className={`block w-full text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md ${
                        !createMode ? 'bg-gray-200' : 'bg-white'
                      }`}
                      value={currentListener?.type}
                      disabled={!createMode}
                      onChange={(e) => {
                        setCurrentListener({
                          ...currentListener,
                          type: e.currentTarget.value,
                          secure:
                            currentListener.secure === null
                              ? false
                              : currentListener.secure,
                          component: '',
                        });
                        setCompUiConfig(undefined);
                      }}
                    >
                      {createMode && (
                        <option value="" disabled>
                          Select source type
                        </option>
                      )}
                      {createMode &&
                        listenerTypes &&
                        listenerTypes.map((type: string, i: number) => (
                          <option key={i} value={type}>
                            {type}
                          </option>
                        ))}
                      {!createMode && (
                        <option value={currentListener.type}>
                          {currentListener.type}
                        </option>
                      )}
                    </select>
                  </div>

                  {/* Security */}
                  <div className="flex flex-col w-full text-black rounded-xl">
                    <label className="block text-sm font-medium">
                      Security
                    </label>
                    <select
                      className={`block w-full text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md ${
                        !createMode ? 'bg-gray-200' : 'bg-white'
                      }`}
                      //value={currentListener?.secure}
                      disabled={!createMode}
                      onChange={(e) => {
                        setCurrentListener({
                          ...currentListener,
                          secure: e.currentTarget.value,
                        });
                        setCompUiConfig(undefined);
                      }}
                    >
                      {createMode && (
                        <>
                          <option key={0} value="" disabled>
                            Select security
                          </option>
                          <option key={2} value="false">
                            NOT SECURE
                          </option>
                          <option key={1} value="true">
                            SECURE
                          </option>
                        </>
                      )}
                      {!createMode && (
                        <option>
                          {currentListener?.connectionInfo?.componentInfo
                            ?.secure
                            ? 'SECURE'
                            : 'NOT SECURE'}
                        </option>
                      )}
                    </select>
                  </div>

                  {/* Component */}
                  <div className="flex flex-col w-full text-black rounded-xl">
                    <label className="block text-sm font-medium">
                      Component
                    </label>
                    <select
                      className={`block w-full text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md ${
                        !createMode ? 'bg-gray-200' : 'bg-white'
                      }`}
                      value={currentListener?.component ?? ''}
                      disabled={!createMode}
                      onChange={(e) => {
                        const component = e.currentTarget.value;
                        let authInfo = null;
                        if (component === 'KAFKA') {
                          authInfo = {
                            type: 'BASIC',
                          };
                        }
                        setCurrentListener((prev) => ({
                          ...prev,
                          component: component,
                          version: component === 'MQTT' ? 'v5' : 'default',
                          authenticationInfo: authInfo,
                          connectionInfo: {
                            ...prev.connectionInfo,
                            componentInfo: {
                              ...prev.connectionInfo?.componentInfo,
                              version: component === 'MQTT' ? 'v5' : 'default',
                            },
                          },
                        }));
                        setIsFormValid(true);
                        setCompUiConfig(() => {
                          if (component === 'KAFKA') {
                            return kafkaUIConfig;
                          } else if (component === 'MQTT') {
                            return mqttUIConfig;
                          } else if (component === 'HTTP') {
                            return httpUIConfig;
                          } else if (
                            component === 'TCP' ||
                            component === 'UDP'
                          ) {
                            return tcpUdpUIConfig;
                          }
                        });
                      }}
                    >
                      {createMode && (
                        <option value="" disabled>
                          Select component
                        </option>
                      )}
                      {createMode &&
                        currentListener.type !== '' &&
                        currentListener.secure !== null &&
                        listenerComponents &&
                        listenerComponents.map(
                          (componentName: string, i: number) => (
                            <option key={i} value={componentName}>
                              {componentName}
                            </option>
                          )
                        )}
                      {!createMode && (
                        <option>
                          {
                            currentListener?.connectionInfo?.componentInfo
                              ?.component
                          }
                        </option>
                      )}
                    </select>
                  </div>

                  {/* MQTT Version */}
                  {currentListener?.type !== 'INTERNAL' &&
                    (() => {
                      if (compUiConfig?.version?.enabled) {
                        return (
                          <div className="flex flex-col w-full text-black rounded-xl">
                            <label className="block text-sm font-medium">
                              {compUiConfig?.version.label}
                            </label>
                            <select
                              className={`block w-full text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md ${
                                !createMode ? 'bg-gray-200' : 'bg-white'
                              }`}
                              value={
                                currentListener?.connectionInfo?.componentInfo
                                  ?.version ?? 'v5'
                              }
                              disabled={!createMode}
                              onChange={(e) => {
                                const mqttVersion = e.currentTarget.value;
                                setCurrentListener((prev) => ({
                                  ...prev,
                                  connectionInfo: {
                                    ...prev.connectionInfo,
                                    componentInfo: {
                                      ...prev.connectionInfo?.componentInfo,
                                      version: mqttVersion,
                                    },
                                  },
                                  version: mqttVersion,
                                }));
                              }}
                            >
                              {createMode && (
                                <option value="" disabled>
                                  Select version
                                </option>
                              )}
                              {createMode && (
                                <>
                                  <option key="v3" value="v3">
                                    v3
                                  </option>
                                  <option key="v5" value="v5">
                                    v5
                                  </option>
                                </>
                              )}
                              {!createMode && (
                                <option>
                                  {currentListener?.connectionInfo
                                    ?.componentInfo?.version ?? 'v5'}
                                </option>
                              )}
                            </select>
                          </div>
                        );
                      }
                    })()}
                </div>
              </div>
            </div>

            {/* Section 3 -> IPAddresses */}
            {getUICfg('ipAddresses') && (
              <div className="px-5 pb-5">
                <div className="bg-[#E3E4E9] rounded-xl p-3">
                  <div className="flex flex-col gap-2 items-center w-full z-10 overflow-x-auto shadow-none">
                    {/* IPAddresses */}
                    <div className="flex flex-col w-full text-black rounded-xl">
                      <label className="block text-sm font-medium">
                        {compUiConfig?.ipAddresses?.label}
                      </label>
                      <textarea
                        className="w-full h-16 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm resize-none"
                        name="description"
                        autoComplete="off"
                        placeholder="e.g., ***********, ***********"
                        maxLength={50}
                        defaultValue={currentListener?.ipAddresses?.join(',')}
                        onChange={(e) => {
                          const ipAddresses = e.currentTarget.value
                            .split(',')
                            .map((item) => item.trim())
                            .filter((item) => item !== '');
                          setCurrentListener({
                            ...currentListener,
                            ipAddresses: ipAddresses,
                          });
                        }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Section 4 -> Component Info */}
            {(getUICfg('compInfo') &&
              currentListener.component !== '' &&
              currentListener?.type === 'EXTERNAL') ||
            isEditInternal() ? (
              <form>
                <fieldset>
                  <div className="px-5 pb-5">
                    <div className="bg-[#E3E4E9] rounded-xl p-4">
                      <div className="flex flex-col gap-3 w-full z-10 overflow-x-auto shadow-none">
                        {/* External Source */}
                        <div className="flex flex-col w-full text-black">
                          <label className="block pb-3 text-sm font-medium">
                            {compUiConfig?.compInfo?.label}
                          </label>

                          {/* Protocol / Host / Port */}
                          <div className="grid grid-cols-8 gap-2">
                            {/* Protocol */}
                            {getUICfg('protocol') && (
                              <div className="flex flex-col w-full col-span-2">
                                <label className="block text-sm font-medium">
                                  {compUiConfig?.protocol?.label}
                                </label>
                                <input
                                  className={
                                    'w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm bg-gray-200'
                                  }
                                  disabled={true}
                                  name="protocol"
                                  value={
                                    currentListener?.connectionInfo
                                      ?.componentInfo?.protocol
                                  }
                                />
                              </div>
                            )}

                            {/* Host */}
                            {getUICfg('host') && (
                              <div className="flex flex-col w-full col-span-4">
                                <label className="block text-sm font-medium">
                                  {compUiConfig?.host?.label}
                                </label>
                                <input
                                  className={`w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm ${
                                    isEditInternal()
                                      ? 'bg-gray-200'
                                      : 'bg-white'
                                  }`}
                                  disabled={isEditInternal()}
                                  name="host"
                                  placeholder="host.example.com"
                                  autoComplete="off"
                                  value={currentListener?.host}
                                  onChange={(e) => {
                                    setCurrentListener((prev) => ({
                                      ...prev,
                                      host: e.target.value,
                                    }));
                                  }}
                                />
                              </div>
                            )}

                            {/* Port */}
                            {getUICfg('port') && (
                              <div className="flex flex-col w-full col-span-2">
                                <label className="block text-sm font-medium">
                                  {compUiConfig?.port?.label}
                                </label>
                                <input
                                  className={`w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm ${
                                    isEditInternal()
                                      ? 'bg-gray-200'
                                      : 'bg-white'
                                  }`}
                                  disabled={isEditInternal()}
                                  name="port"
                                  type="number"
                                  min={portMin}
                                  max={portMax}
                                  autoComplete="off"
                                  placeholder={`Between ${portMin} and ${portMax}`}
                                  value={currentListener?.port}
                                  onChange={(e) => {
                                    const inputValue = e.target.value;
                                    if (
                                      inputValue === '' ||
                                      (Number(inputValue) >= portMin &&
                                        Number(inputValue) <= portMax)
                                    ) {
                                      setCurrentListener((prev) => ({
                                        ...prev,
                                        port: e.target.value,
                                      }));
                                    }
                                  }}
                                />
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Hostname */}
                        {getUICfg('hostname') && (
                          <div className="flex flex-col w-full text-black">
                            <div className="flex flex-col w-full">
                              <label className="block text-sm font-medium">
                                {compUiConfig?.hostname?.label}
                              </label>
                              <input
                                className={
                                  'w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm bg-gray-200'
                                }
                                disabled={true}
                                name="hostname"
                                placeholder=""
                                autoComplete="off"
                                value={
                                  currentListener?.connectionInfo
                                    ?.externalAccessInfo?.hostname
                                }
                              />
                            </div>
                          </div>
                        )}

                        {/* Identifier */}
                        {getUICfg('identifier') && (
                          <div className="flex flex-col w-full text-black">
                            <label className="block text-sm font-medium">
                              {compUiConfig?.identifier?.label}
                            </label>
                            <input
                              className={`w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm ${
                                isEditInternal() ? 'bg-gray-200' : 'bg-white'
                              }`}
                              disabled={isEditInternal()}
                              name="identifier"
                              placeholder={compUiConfig?.identifier?.label}
                              autoComplete="off"
                              defaultValue={
                                currentListener?.connectionInfo?.componentInfo
                                  ?.identifier
                              }
                              onChange={(e) => {
                                const target = e.currentTarget;
                                if (target && target.value) {
                                  setCurrentListener((prev) => ({
                                    ...prev,
                                    componentSpecificProps: {
                                      ...prev.componentSpecificProps,
                                      identifier: target.value,
                                    },
                                  }));
                                }
                              }}
                            />
                          </div>
                        )}

                        {/* ConnectionId */}
                        {getUICfg('connectionId') && (
                          <div className="flex flex-col w-full text-black">
                            <label className="block text-sm font-medium">
                              {compUiConfig?.connectionId?.label}
                            </label>
                            <input
                              className={`w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm ${
                                isEditInternal() ? 'bg-gray-200' : 'bg-white'
                              }`}
                              disabled={isEditInternal()}
                              name="connectionId"
                              placeholder={compUiConfig?.connectionId?.label}
                              autoComplete="off"
                              defaultValue={
                                currentListener?.componentSpecificProps
                                  ?.connectionId
                              }
                              onChange={(e) => {
                                const target = e.currentTarget;
                                if (target && target.value) {
                                  setCurrentListener((prev) => ({
                                    ...prev,
                                    componentSpecificProps: {
                                      ...prev.componentSpecificProps,
                                      connectionId: target.value,
                                    },
                                  }));
                                }
                              }}
                            />
                          </div>
                        )}

                        {/* Basic Authentication Switch*/}
                        {getUICfg('basicAuthSwitch') &&
                          currentListener?.type !== 'INTERNAL' && (
                            <div className="flex flex-col w-full text-black">
                              <label className="block text-sm font-medium">
                                {compUiConfig?.basicAuthSwitch?.label}
                              </label>
                              <Switch
                                label={''}
                                checked={
                                  currentListener?.authenticationInfo?.type ===
                                  'BASIC'
                                }
                                disabled={
                                  !createMode &&
                                  currentListener?.type !== 'EXTERNAL'
                                }
                                onChange={(e) => {
                                  setCurrentListener((prev) => ({
                                    ...prev,
                                    authenticationInfo: e
                                      ? {
                                          ...prev.authenticationInfo,
                                          type: 'BASIC',
                                        }
                                      : {
                                          ...prev.authenticationInfo,
                                          type: null,
                                        },
                                  }));
                                }}
                              />
                            </div>
                          )}

                        {/* Basic Authentication */}
                        {getUICfg('basicAuth') &&
                          currentListener?.authenticationInfo?.type && (
                            <div className="flex flex-col w-full text-black">
                              <label className="block text-sm font-medium">
                                {compUiConfig?.basicAuth?.label}
                              </label>
                              <div className="grid grid-cols-2 gap-4">
                                <input
                                  className={`w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm ${
                                    isEditInternal()
                                      ? 'bg-gray-200'
                                      : 'bg-white'
                                  }`}
                                  disabled={isEditInternal()}
                                  name="username"
                                  placeholder="username"
                                  autoComplete="off"
                                  defaultValue={
                                    currentListener?.authenticationInfo
                                      ?.username ?? ''
                                  }
                                  onChange={(e) => {
                                    const target = e.currentTarget;
                                    if (target && target.value) {
                                      setCurrentListener((prev) => ({
                                        ...prev,
                                        authenticationInfo: {
                                          ...prev.authenticationInfo,
                                          username: target.value,
                                        },
                                      }));
                                    }
                                  }}
                                />
                                <input
                                  className={`w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm ${
                                    isEditInternal() ||
                                    currentListener?.authenticationInfo
                                      ?.type !== 'BASIC'
                                      ? 'bg-gray-200'
                                      : 'bg-white'
                                  }`}
                                  disabled={
                                    isEditInternal() ||
                                    currentListener?.authenticationInfo
                                      ?.type !== 'BASIC'
                                  }
                                  name="password"
                                  placeholder="password"
                                  autoComplete="off"
                                  defaultValue={
                                    currentListener?.authenticationInfo
                                      ?.password ?? ''
                                  }
                                  onChange={(e) => {
                                    const target = e.currentTarget;
                                    if (target && target.value) {
                                      setCurrentListener((prev) => ({
                                        ...prev,
                                        authenticationInfo: {
                                          ...prev.authenticationInfo,
                                          password: target.value,
                                        },
                                      }));
                                    }
                                  }}
                                />
                              </div>
                            </div>
                          )}

                        {/* Connection Properties */}
                        {getUICfg('conProps') && (
                          <div className="flex flex-col w-full text-black">
                            <label className="block text-sm font-medium">
                              {compUiConfig?.conProps?.label}
                            </label>

                            {!isEditInternal() &&
                              currentListener?.componentSpecificConnPropsJson &&
                              (isJsonStringValidObject(
                                currentListener?.componentSpecificConnPropsJson
                              ) ? (
                                <span className="ml-4 flex-row items-center inline-flex">
                                  <span className=" text-green-600 text-xs">
                                    Valid JSON
                                  </span>
                                  <button
                                    type="button"
                                    onClick={() => {
                                      let prop =
                                        currentListener?.componentSpecificConnPropsJson ??
                                        '{}';
                                      setCurrentListener((prev) => ({
                                        ...prev,
                                        componentSpecificConnPropsJson:
                                          JSON.stringify(
                                            JSON.parse(prop),
                                            null,
                                            2
                                          ),
                                      }));
                                    }}
                                    className="bg-slate-200 text-xs flex flex-row items-center justify-center border border-slate-300 rounded-md   ml-2 px-1 hover:bg-slate-300"
                                  >
                                    Beautify
                                  </button>
                                </span>
                              ) : (
                                <span className="ml-4 text-red-500 text-xs">
                                  Invalid JSON
                                </span>
                              ))}
                            <textarea
                              className={`w-full h-64 rounded-md shadow-sm placeholder-gray-300 border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm font-light resize-none ${
                                isEditInternal() ? 'bg-gray-200' : 'bg-white'
                              }`}
                              disabled={isEditInternal()}
                              name="componentOptions"
                              autoComplete="off"
                              placeholder=""
                              maxLength={1024}
                              value={
                                currentListener?.componentSpecificConnPropsJson ??
                                '{}'
                              }
                              onChange={(e) => {
                                let value = e.currentTarget.value;
                                if (!value) {
                                  value = '{}';
                                }
                                {
                                  isJsonStringValidObject(value)
                                    ? setCurrentListener((prev) => ({
                                        ...prev,
                                        componentSpecificConnPropsJson: value,
                                        componentSpecificConnProps:
                                          JSON.parse(value),
                                      }))
                                    : setCurrentListener((prev) => ({
                                        ...prev,
                                        componentSpecificConnPropsJson: value,
                                        componentSpecificConnProps: value,
                                      }));
                                }
                              }}
                            />
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </fieldset>
              </form>
            ) : undefined}
          </div>
        </div>

        {/* Footer with Create Button */}
        <div className="p-3 border-t border-gray-300">
          {!createMode && (
            <div className="flex flex-col items-center w-full z-10 overflow-x-auto shadow-none">
              <div className="flex flex-col w-full text-left rounded-xl gap-0 px-4 pb-2">
                {currentListener?.createdDate && (
                  <div className="w-full grid grid-cols-[250px_1fr]">
                    <span className="text-gray-500 text-sm">
                      Created by {currentListener.user}:
                    </span>
                    <p className="text-gray-500 text-sm">
                      {Utils.formatISODate(currentListener?.createdDate)}
                    </p>
                  </div>
                )}
                {currentListener?.updatedDate && (
                  <div className="w-full grid grid-cols-[100px_1fr]">
                    <span className="text-gray-500 text-sm">Last edited:</span>
                    <p className="text-gray-500 text-sm">
                      {Utils.formatISODate(currentListener?.updatedDate)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
          <div className="flex justify-end">
            {createMode && (
              <button
                disabled={currentListener?.component === '' || !isFormValid}
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onCreateClick}
              >
                {'Create'}
              </button>
            )}

            {!createMode && (
              <button
                disabled={!isFormValid || !canSave}
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onSaveClick}
              >
                {'Save'}
              </button>
            )}

            {!createMode &&
              listener?.type === 'EXTERNAL' &&
              listener?.connectionInfo?.componentInfo?.secure && (
                <button
                  disabled={canSave}
                  className="px-3 mx-1 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                  onClick={onNextClick}
                >
                  {'Next'}
                </button>
              )}
          </div>
        </div>
      </div>
    </div>
  );
};
