export interface SchReport {
  id: string;
  name: string;
  description: string;
  status: string;
  eventId: string;
  eventName: string;
  filterInfo: FilterInfo;
  scheduleInfo: ScheduleInfo;
  deliveryInfo: DeliveryInfo;
  createdAt: string;
  updatedAt: string | null;
}

export interface FilterInfo {
  filters: {
    [field: string]: FilterCondition;
  };
  operator: 'AND' | 'OR';
  sort: {
    field: string;
    asc: boolean;
  };
}

export interface ScheduleInfo {
  cronExpression: string;
  readableCronExpression: string;
  type: 'WEEKLY' | 'DAILY' | 'MONTHLY' | string;
  config: {
    hour: number;
    minute: number;
    daysOfWeek?: string[]; // as string numbers e.g. ["1", "5"]
    dayOfMonth?: number | null;
  };
  lastRunAt: string | null;
  nextRunAt: string | null;
}

export interface DeliveryInfo {
  target: {
    targetType: 'EMAIL' | string;
    targetRecipients: string[];
  };
  status: 'UNKNOWN' | 'PENDING' | 'SENT' | string;
  lastAttemptAt: string | null;
  failureDetails: string | null;
}

export type FilterCondition =
  | {
      gte?: number | string;
      lte?: number | string;
      eq?: number | string | boolean;
      like?: string;
      gt?: number | string;
      lt?: number | string;
    }
  | {}; // allow empty filters
