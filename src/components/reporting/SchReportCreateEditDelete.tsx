import { useFlagcard } from '@/contexts/flagcard-context';
import { storyService } from '@/services/dojo-api-gateway/story-service';

import { Logger } from '@/utils/Logger';
import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import useEvent from 'react-use-event-hook';
import { useLoader } from '../loaderIndicator/LoaderContext';

import { usePrompt } from '@/contexts/prompt-context/usePrompt';
import {
  createSchReporting,
  deleteSchReport,
  updateSchReport,
} from '@/services/dojo-api-gateway/story-service/story/story';
import { Box, FormControl } from '@mui/material';
import Cron, { CronType, PeriodType } from 'react-js-cron';
import Switch from '../switch/Switch';
import {
  DeliveryInfo,
  FilterInfo,
  ScheduleInfo,
  SchReport,
} from './SchReport.specs';
import { userRoles } from '@/contexts/auth-context/useAuth';

export const SchReportCreateEditDelete: React.FC<{
  createMode: Boolean;
  schReport?: SchReport | null;
  filterInfo?: FilterInfo | null;
  eventId: string;
  callback: (schReport?: SchReport) => void;
  close: () => void;
}> = ({ createMode, schReport, filterInfo, eventId, callback, close }) => {
  const flagcard = useFlagcard();
  const { showLoader, hideLoader } = useLoader();
  Logger.debug(schReport);

  const prompt = usePrompt();
  const roles = userRoles();

  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [canSave, setCanSave] = useState<boolean>(false);
  const [currentSchReport, setCurrentSchReport] = useState<SchReport>(
    schReport ??
      ({
        name: 'New scheduled report',
      } as SchReport)
  );

  const allowedDropdowns: CronType[] = [
    'period',
    'months',
    'month-days',
    'week-days',
    'hours',
    'minutes',
  ];
  const allowedPeriods: PeriodType[] = ['month', 'week', 'hour', 'minute'];

  useEffect(() => {
    const hasChanges =
      JSON.stringify(currentSchReport) !== JSON.stringify(currentSchReport);
    setCanSave(hasChanges);
  }, [currentSchReport]);

  const [cronValue, setCronValue] = useState('0 0 * * *');
  const [selectedSchReport, setSelectedSchReport] = useState('');
  const [formData, setFormData] = useState<SchReport>({
    name: '',
    scheduleInfo: {} as ScheduleInfo,
    deliveryInfo: {} as DeliveryInfo,
    filterInfo: filterInfo ?? ({} as FilterInfo),
    status: 'PASSIVE',
  } as unknown as SchReport);

  // Initialize formData when editing an existing scheduled report
  useEffect(() => {
    if (!createMode && schReport) {
      setFormData({
        ...schReport,
        filterInfo: filterInfo ?? schReport.filterInfo,
        status: schReport.status === 'ACTIVE' ? 'ACTIVE' : 'PASSIVE',
      });
      setSelectedSchReport(schReport.name);
      setCronValue(schReport.scheduleInfo.cronExpression);
    } else {
      setFormData({
        name: 'New scheduled Report',
        eventId: eventId,
        scheduleInfo: {
          cronExpression: '0 0 * * *',
        },
        deliveryInfo: {
          target: {
            targetType: 'EMAIL',
            targetRecipients: [],
          },
        },
        filterInfo: filterInfo ?? {
          filters: {},
          operator: 'AND',
          sort: {
            field: '@timestamp',
            asc: false,
          },
        },
        status: 'PASSIVE',
      } as unknown as SchReport);
    }
  }, [createMode, schReport]);

  const { data: schReports, refetch: refetchSchReport } = useQuery({
    queryKey: ['schReports'],
    queryFn: async () => {
      showLoader();
      const response = await storyService.story.scheduledReports(eventId);
      hideLoader();
      return response.data?.Data?.content;
    },
    onError: () => {
      // flagcard.appear({
      //   type: 'error',
      //   message: error.message,
      // });
      // hideLoader();
    },
    refetchOnMount: true,
    refetchOnWindowFocus: false,
    enabled: true,
  });

  const { data: toggleStatus, refetch: refetchToggleStatus } = useQuery({
    queryKey: ['toogleSchReportStatus'],
    queryFn: async () => {
      showLoader();
      const response = await storyService.story.toggleSchReportStatus(
        currentSchReport.id
      );
      hideLoader();
      return response.data?.Data?.content;
    },
    onError: () => {
      // flagcard.appear({
      //   type: 'error',
      //   message: error.message,
      // });
      // hideLoader();
    },
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: false,
  });

  // const handleChange = (event: SelectChangeEvent) => {
  //   const selectedName = event.target.value;
  //   setSelectedReport(selectedName);
  //   // Find the selected schedule and update the form data
  //   const schedule = schReports?.find(
  //     (s: ScheduledReport) => s.name === selectedName
  //   );
  //   if (schedule) {
  //     //schedule.status = 'ACTIVE';
  //     setFormData({
  //       ...formData,
  //       name: schedule.name,
  //       scheduleInfo: schedule.scheduleInfo,
  //       deliveryInfo: schedule.deliveryInfo,
  //       filterInfo: schedule.filterInfo,
  //       status: schedule.status !== 'PASSIVE',
  //     });
  //   }
  // };

  const onCreateClick = useEvent(() => {
    let result: any;
    showLoader();
    Logger.debug(currentSchReport);
    const newSchReportUpdate = { ...formData };
    let createdListener: any;
    createSchReporting(newSchReportUpdate)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        createdListener = response.data.Data.content;
        callback(createdListener);
        close();
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        // if (createdListener != null) {
        //   refetchSchReport().then(() => {
        //     if (
        //       createdListener.type === 'EXTERNAL' &&
        //       createdListener?.connectionInfo?.componentInfo?.secure
        //     ) {
        //       callback(createdListener);
        //     } else {
        //       close();
        //     }
        //   });
        // }
      });
  });

  const onSaveClick = useEvent(() => {
    let result: any;
    Logger.debug(currentSchReport);
    showLoader();

    const schReportToUpdate = { ...formData };
    let updatedSchReport: any;
    updateSchReport(schReportToUpdate)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        updatedSchReport = response.data.Data.content;
        //setCurrentSchReport(updatedSchReport);
        console.log(currentSchReport);
        if (schReport) {
          callback(updatedSchReport);
        }
        Logger.debug(updatedSchReport);
        close();
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        if (updatedSchReport != null) {
          refetchSchReport().then(() => {});
        }
      });
  });

  const onDeleteClick = useEvent(() => {
    prompt
      .asyncAppear({
        id: 'delete-action',
        key: 'delete-action',
        title: 'Are you sure about the delete this scheduled reporting?',
      })
      .then((e) => {
        if (!e) return;
        let result: any;
        Logger.debug(currentSchReport);
        showLoader();
        deleteSchReport(currentSchReport.id as string)
          .then((response: any) => {
            result = { type: 'success', message: response.data.Message };
            callback();
            close();
            //refetchSchReport();
          })
          .catch((err: any) => {
            result = {
              type: 'error',
              message: err.response.data.Error.content[0],
            };
          })
          .finally(() => {
            flagcard.appear({
              type: result.type,
              message: result.message,
            });
            hideLoader();
          });
      });
  });

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div className="w-[420px] min-h-[580px] max-h-[680px]  bg-[#ededed] shadow-lg rounded-xl  flex flex-col">
        {/* Header */}
        <div className="flex justify-between items-center border-gray-300 p-4">
          <h3 className="text-lg font-medium">
            {createMode ? 'New Scheduled Reporting' : currentSchReport.name}
          </h3>
          <button
            className="text-gray-600 hover:text-gray-800"
            onClick={() => {
              close();
            }}
            aria-label="Close"
          >
            &times;
          </button>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-scroll">
          <div style={{ display: 'block' }}>
            {/* Section 1 -> ListenerName | Description | Event */}
            <div className="px-5 pb-5">
              <div className="bg-[#E3E4E9] rounded-xl p-3">
                <div className="flex flex-col gap-2 items-center w-full z-10 overflow-x-auto shadow-none">
                  <Box>
                    {/* <FormControl fullWidth>
                      <div className="flex flex-col w-full text-black rounded-xl"></div>
                      <label className="block text-sm font-medium">
                        Scheduled Reportings
                      </label>
                      <select
                        className="block w-full pl-2 pr-10 py-2 text-base border-black border-2 focus:ring-1 focus:ring-black focus:border-black sm:text-sm rounded-md"
                        id="scheduled-report-select"
                        value={selectedSchReport}
                        onChange={(e) => {
                          const selectedName = e.target.value;
                          setSelectedSchReport(selectedName);
                          // Find the selected schedule and update the form data
                          const schedule = schReports?.find(
                            (s: SchReport) => s.name === selectedName
                          );
                          setCurrentSchReport(schedule);
                          if (schedule) {
                            // Map the schedule data to match formData structure
                            setFormData({
                              ...schedule,
                              status:
                                schedule.status === 'ACTIVE'
                                  ? 'ACTIVE'
                                  : 'PASSIVE',
                            });
                          }
                        }}
                      >
                        <option value="" disabled>
                          Select an scheduled reporting
                        </option>
                        {schReports &&
                          schReports?.map(
                            (schedule: SchReport, index: number) => (
                              <option key={index} value={schedule.name}>
                                {schedule.name}
                              </option>
                            )
                          )}
                      </select>
                    </FormControl> */}
                    <FormControl
                      fullWidth
                      // sx={{ mt: 2 }}
                    >
                      <label className="block text-sm font-medium">Name</label>
                      <input
                        className="w-full h-9 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
                        name="name"
                        placeholder="scheduled_report_1"
                        autoComplete="off"
                        defaultValue={formData?.name}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            name: e.target.value,
                          })
                        }
                      />
                    </FormControl>

                    {/* Cron */}
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <label className="block text-sm font-medium">
                        Interval
                      </label>
                      <div className="flex flex-wrap gap-2 w-full">
                        <Cron
                          value={cronValue}
                          setValue={(value: string) => {
                            setCronValue(value);
                            setFormData({
                              ...formData,
                              scheduleInfo: {
                                ...formData.scheduleInfo,
                                cronExpression: value,
                              },
                            });
                          }}
                          allowedDropdowns={allowedDropdowns}
                          allowedPeriods={allowedPeriods}
                        />
                      </div>
                    </FormControl>

                    <FormControl>
                      <label className="block text-sm font-medium">
                        CRON Expression {cronValue}
                      </label>
                    </FormControl>

                    {/* Target Emails */}
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <label className="block text-sm font-medium">
                        Target Emails
                      </label>
                      <textarea
                        className="w-full min-h-32 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
                        name="targetEmails"
                        autoComplete="off"
                        placeholder="<EMAIL>"
                        defaultValue={formData.deliveryInfo?.target?.targetRecipients?.join(
                          '\n'
                        )}
                        onChange={(e) => {
                          const targetRecipients = e.target.value
                            .split('\n')
                            .map((item) => item.trim())
                            .filter((item) => item !== '');
                          setFormData({
                            ...formData,
                            deliveryInfo: {
                              ...formData.deliveryInfo,
                              target: {
                                ...formData.deliveryInfo?.target,
                                targetRecipients: targetRecipients,
                              },
                            },
                          });
                        }}
                      />
                    </FormControl>
                    {/* Filters */}
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <label className="block text-sm font-medium">
                        Filters
                      </label>
                      <textarea
                        className="w-full min-h-32 rounded-md shadow-sm placeholder-gray-300 text-black border-2 border-black focus:outline-none focus:ring-1 focus:ring-black focus:border-black sm:text-sm"
                        name="filters"
                        autoComplete="off"
                        disabled={true}
                        value=()=>{
                          return JSON.stringify(formData.filterInfo, null, 2);
                        }
                      />
                    </FormControl>
                    <FormControl fullWidth sx={{ mt: 2 }}>
                      <label className="block text-sm font-medium">
                        Status
                      </label>
                      <Switch
                        label={''}
                        //checked={formData.status !== 'PASSIVE'}
                        defaultChecked={formData.status !== 'PASSIVE'}
                        onChange={(e) => {
                          refetchToggleStatus();
                        }}
                      />
                    </FormControl>
                  </Box>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer with Create Button */}
        <div className="p-3 border-t border-gray-300">
          <div className="flex justify-end gap-2">
            {roles.createReports && createMode && (
              <button
                // disabled={currentListener?.component === '' || !isFormValid}
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onCreateClick}
              >
                {'Create'}
              </button>
            )}

            {roles.deleteReports && !createMode && (
              <button
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onDeleteClick}
              >
                {'Delete'}
              </button>
            )}

            {roles.updateReports && !createMode && (
              <button
                // disabled={!isFormValid || !canSave}
                className="px-3 h-[24px] w-[96px] text-white bg-[#5B3085] disabled:bg-[#999] flex justify-center items-center text-xs rounded-md"
                onClick={onSaveClick}
              >
                {'Save'}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
