import { useFlagcard } from '@/contexts/flagcard-context';
import { storyService } from '@/services/dojo-api-gateway/story-service';

import { Logger } from '@/utils/Logger';
import { useQuery } from '@tanstack/react-query';
import React, { useEffect, useState } from 'react';
import useEvent from 'react-use-event-hook';
import { useLoader } from '../loaderIndicator/LoaderContext';

import {
  createSchReporting,
  deleteSchReport,
  updateSchReport,
} from '@/services/dojo-api-gateway/story-service/story/story';
import SchReportIcon from '@mui/icons-material/Schedule';
import {
  Box,
  FormControl,
  IconButton,
  InputLabel,
  Menu,
  MenuItem,
  Tooltip,
} from '@mui/material';
import {
  DeliveryInfo,
  FilterInfo,
  ScheduleInfo,
  SchReport,
} from './SchReport.specs';

import { useGridApiContext } from '@mui/x-data-grid-pro';

export const SchReportToolbar: React.FC<{
  createMode: Boolean;
  schReport?: SchReport | null;
  eventId: string;
  callback: (currentListener?: ListenerConfig) => void;
  close: () => void;
}> = ({ createMode, schReport, eventId, callback, close }) => {
  const flagcard = useFlagcard();
  const { showLoader, hideLoader } = useLoader();
  Logger.debug(schReport);

  const portMin = 0;
  const portMax = 65535;

  const [isFormValid, setIsFormValid] = useState<boolean>(false);
  const [canSave, setCanSave] = useState<boolean>(false);
  const [currentSchReport, setCurrentSchReport] = useState<SchReport>(
    schReport ?? ({} as SchReport)
  );

  // useEffect(() => {
  //   const hasChanges =
  //     JSON.stringify(currentSchReport) !== JSON.stringify(currentSchReport);
  //   setCanSave(hasChanges);
  // }, [currentSchReport]);

  const [value, setValue] = useState('0 0 * * *');
  const [selectedSchReport, setSelectedSchReport] = useState('');
  const [formData, setFormData] = useState<SchReport>({
    name: '',
    scheduleInfo: {} as ScheduleInfo,
    deliveryInfo: {} as DeliveryInfo,
    filterInfo: {} as FilterInfo,
    status: 'PASSIVE',
  } as unknown as SchReport);

  // Initialize formData when editing an existing scheduled report
  useEffect(() => {
    if (!createMode && schReport) {
      setFormData({
        ...schReport,
        status: schReport.status === 'ACTIVE' ? 'ACTIVE' : 'PASSIVE',
      });
      setSelectedSchReport(schReport.name);
    }
  }, [createMode, schReport]);

  const { data: schReports, refetch: refetchSchReport } = useQuery({
    queryKey: ['schReports'],
    queryFn: async () => {
      showLoader();
      const response = await storyService.story.scheduledReports(eventId);
      hideLoader();
      return response.data?.Data?.content;
    },
    onError: () => {
      // flagcard.appear({
      //   type: 'error',
      //   message: error.message,
      // });
      // hideLoader();
    },
    refetchOnMount: false,
    refetchOnWindowFocus: false,
    enabled: true,
  });

  const onCreateClick = useEvent(() => {
    let result: any;
    showLoader();
    Logger.debug(currentSchReport);

    let createdListener: any;
    createSchReporting(currentSchReport)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        createdListener = response.data.Data.content;
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        if (createdListener != null) {
          refetchSchReport().then(() => {
            if (
              createdListener.type === 'EXTERNAL' &&
              createdListener?.connectionInfo?.componentInfo?.secure
            ) {
              callback(createdListener);
            } else {
              close();
            }
          });
        }
      });
  });

  const onSaveClick = useEvent(() => {
    let result: any;
    Logger.debug(currentSchReport);
    showLoader();

    const schReportToUpdate = { ...formData };

    // deleteSchReport(currentSchReport.id as string)
    //   .then((response: any) => {
    //     result = { type: 'success', message: response.data.Message };
    //     refetchSchReport();
    //   })
    //   .catch((err: any) => {
    //     result = {
    //       type: 'error',
    //       message: err.response.data.Error.content[0],
    //     };
    //   })
    //   .finally(() => {
    //     flagcard.appear({
    //       type: result.type,
    //       message: result.message,
    //     });
    //     hideLoader();
    //   });

    let updatedSchReport: any;
    updateSchReport(schReportToUpdate)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        updatedSchReport = response.data.Data.content;
        //setCurrentSchReport(updatedSchReport);
        console.log(currentSchReport);
        if (schReport) {
          callback(updatedSchReport);
        }
        Logger.debug(updatedSchReport);
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        hideLoader();
        flagcard.appear({
          type: result.type,
          message: result.message,
        });

        if (updatedSchReport != null) {
          refetchSchReport().then(() => {});
        }
      });
  });

  const onDeleteClick = useEvent(() => {
    let result: any;
    Logger.debug(currentSchReport);
    showLoader();
    deleteSchReport(currentSchReport.id as string)
      .then((response: any) => {
        result = { type: 'success', message: response.data.Message };
        refetchSchReport();
      })
      .catch((err: any) => {
        result = {
          type: 'error',
          message: err.response.data.Error.content[0],
        };
      })
      .finally(() => {
        flagcard.appear({
          type: result.type,
          message: result.message,
        });
        hideLoader();
      });
  });

  const apiRef = useGridApiContext();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box>
      <FormControl fullWidth>
        <Tooltip title="Scheduled Reports">
          <IconButton
            onClick={handleClick}
            aria-label="Select a scheduled report"
            aria-controls={open ? 'scheduled-report-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={open ? 'true' : undefined}
            sx={{
              color: '#5b3085',
              '&:hover': {
                backgroundColor: 'rgba(91, 48, 133, 0.04)',
              },
            }}
          >
            {' '}
            <SchReportIcon fontSize="small" />
          </IconButton>
        </Tooltip>
        <Menu
          id="scheduled-report-menu"
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          slotProps={{
            list: {
              'aria-labelledby': 'scheduled-report-button',
              role: 'listbox',
            },
          }}
        >
          <MenuItem value="" disabled>
            Select an scheduled reporting
          </MenuItem>
          {schReports &&
            schReports?.map((schedule: SchReport, index: number) => (
              <MenuItem
                role="option"
                sx={{
                  color: '#5b3085',
                  '&:hover': {
                    backgroundColor: 'rgba(91, 48, 133, 0.04)',
                  },
                }}
                key={index}
                value={schedule.name}
                onClick={() => {}}
              >
                {schedule.name}
              </MenuItem>
            ))}
        </Menu>
      </FormControl>
      <FormControl fullWidth>
        <InputLabel id="scheduled-report-label">Scheduled Reports</InputLabel>
      </FormControl>
    </Box>
  );

  // (

  //   <Box sx={{ height: '32px' }}>
  //     <FormControl fullWidth>
  //       <InputLabel id="dynamic-select-label">Select Option</InputLabel>
  //       <Select
  //         sx={{ height: '32px' }}
  //         labelId="dynamic-select-label"
  //         value={selectedSchReport}
  //         label="Select Option"
  //         onChange={(e) => {
  //           const selectedName = e.target.value;
  //           setSelectedSchReport(selectedName);
  //           // Find the selected schedule and update the form data
  //           const schedule = schReports?.find(
  //             (s: SchReport) => s.name === selectedName
  //           );
  //           setCurrentSchReport(schedule);
  //           if (schedule) {
  //             // Map the schedule data to match formData structure
  //             setFormData({
  //               ...schedule,
  //               status: schedule.status === 'ACTIVE' ? 'ACTIVE' : 'PASSIVE',
  //             });
  //           }
  //         }}
  //       >
  //         <option value="" disabled>
  //           Select an scheduled reporting
  //         </option>
  //         {schReports &&
  //           schReports?.map((schedule: SchReport, index: number) => (
  //             <MenuItem key={index} value={schedule.name}>
  //               {schedule.name}
  //             </MenuItem>
  //           ))}
  //       </Select>
  //     </FormControl>
  //   </Box>
  // );
};
