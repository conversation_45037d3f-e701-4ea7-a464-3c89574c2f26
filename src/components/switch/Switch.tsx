/* This example requires Tailwind CSS v2.0+ */
import { FC, useState, useEffect } from "react"
import { Switch as _Switch } from "@headlessui/react"
import classNames from "classnames"
import useEvent from "react-use-event-hook"

type SwitchProps = {
  label: string
  checked?: boolean
  defaultChecked?: boolean
  onChange?(checked: boolean): void
  disabled?: boolean
}
const Switch: FC<SwitchProps> = (props) => {
  const [enabled, setEnabled] = useState(props.checked ?? props.defaultChecked ?? false)

  // Update internal state when checked prop changes
  useEffect(() => {
    if (props.checked !== undefined) {
      setEnabled(props.checked)
    }
  }, [props.checked])

  const handleChange = useEvent((val) => {
    setEnabled(val)
    props.onChange?.(val)
  })

  return (
    <_Switch.Group as="div" className="flex items-center">
      <_Switch
        disabled={props.disabled ? props.disabled : false}
        checked={props.checked}
        defaultChecked={props.defaultChecked}
        onChange={handleChange}
        className={classNames(
          (props.checked ?? enabled) ? "bg-[#5B3085]" : "bg-[#B3B3B3]",
          "relative inline-flex flex-shrink-0 h-6 w-12 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200"
        )}
        // className={classNames(
        //   enabled ? "bg-black" : "bg-gray-200",
        //   "relative inline-flex flex-shrink-0 h-6 w-11 border-2 border-transparent rounded-full cursor-pointer transition-colors ease-in-out duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-black"
        // )}
      >
        <span
          aria-hidden="true"
          className={classNames(
            (props.checked ?? enabled) ? "translate-x-6" : "translate-x-0",
            "pointer-events-none inline-block h-5 w-5 rounded-full bg-white shadow transform ring-0 transition ease-in-out duration-200"
          )}
        />
      </_Switch>
      <_Switch.Label as="span" className="ml-3">
        <span className="text-sm font-medium text-gray-900">{props.label}</span>
      </_Switch.Label>
    </_Switch.Group>
  )
}
export default Switch
