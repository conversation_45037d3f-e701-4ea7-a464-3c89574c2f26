@tailwind base;
@tailwind components;
@tailwind utilities;

.viewer::after {
  content: '';
  flex-grow: 1000000000;
}

@font-face {
  font-family: 'Concord';
  font-style: normal;
  font-weight: 100;
  src: url(./assets/fonts/Concord-Thin.woff) format('woff');
}

@font-face {
  font-family: 'Concord';
  font-style: normal;
  font-weight: 200;
  src: url(./assets/fonts/Concord-ExtraLight.woff) format('woff');
}

@font-face {
  font-family: 'Concord';
  font-style: normal;
  font-weight: 300;
  src: url(./assets/fonts/Concord-Light.woff) format('woff');
}

@font-face {
  font-family: 'Concord';
  font-style: normal;
  font-weight: 400;
  src: url(./assets/fonts/Concord.woff) format('woff');
}

@font-face {
  font-family: 'Concord';
  font-style: normal;
  font-weight: 500;
  src: url(./assets/fonts/Concord-Medium.woff) format('woff');
}

@font-face {
  font-family: 'Concord';
  font-style: normal;
  font-weight: 700;
  src: url(./assets/fonts/Concord-Bold.woff) format('woff');
}

@font-face {
  font-family: 'Concord';
  font-style: normal;
  font-weight: 900;
  src: url(./assets/fonts/Concord-ExtraBold.woff) format('woff');
}

* {
  font-size: 13px;
  font-family: 'Concord';
}

body {
  --tw-bg-opacity: 1;
  background-color: rgb(241 245 249 / var(--tw-bg-opacity));
}

.folder-icon {
  display: flex;
  flex-direction: column;
}

.item-label {
  font-style: normal;
  font-weight: 700;
  font-size: 12px;
  line-height: 15px;
}

.item-last-update {
  font-style: normal;
  font-size: 12px;
  line-height: 15px;
}

.item-subtext {
  font-style: normal;
  font-weight: 300;
  font-size: 12px;
  line-height: 15px;
}

.main-content::-webkit-scrollbar {
  width: 9px;
  border-radius: 5px;
}

.main-content::-webkit-scrollbar-track {
  background-image: linear-gradient(#b3b3b3, transparent);
  border-radius: 5px;
}

.main-content::-webkit-scrollbar-thumb {
  background-color: #b3b3b3;
  border-radius: 5px;
}

.nice-scroll::-webkit-scrollbar {
  width: 9px;
  border-radius: 5px;
}

.nice-scroll::-webkit-scrollbar-track {
  background-image: linear-gradient(#b3b3b34d, transparent);
  border-radius: 5px;
}

.nice-scroll::-webkit-scrollbar-thumb {
  background-color: #b3b3b3;
  border-radius: 5px;
}

.nice-scroll-x::-webkit-scrollbar {
  height: 0.6rem !important;
  border-radius: 999px !important;
}

.nice-library-scroll::-webkit-scrollbar {
  width: 9px;
  border-radius: 5px;
}

.nice-library-scroll::-webkit-scrollbar-track {
  background-image: transparent;
  border-radius: 5px;
}

.nice-library-scroll::-webkit-scrollbar-thumb {
  background-color: #b3b3b3;
  border-radius: 5px;
}

.nice-library-scroll-x::-webkit-scrollbar {
  height: 0.6rem !important;
  border-radius: 999px !important;
}

.json-inspector__key,
.json-inspector__value {
  position: relative;
}

.json-inspector__selection {
  display: block;
  position: absolute;
  top: -1px;
  left: -3px;
  right: 0;
  z-index: 1;
  padding: 0 3px;

  font: 1em/1 Consolas, monospace;
  outline: none;
  border: none;
  opacity: 0;
  box-shadow: 3px 3px 4px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(0, 0, 0, 0.3);
  color: #222;
}

.json-inspector__selection:focus {
  opacity: 1;
}

.hidden-scrollbar::-webkit-scrollbar {
  display: none;
}

.hidden-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.full-text {
  text-align: justify;
  /* For Edge */
  -moz-text-align-last: justify;
  /* For Firefox prior 58.0 */
  text-align-last: justify;
}

#app {
  height: 100%;
}

html,
body {
  position: relative;
  height: 100%;
}

body {
  background: #eee;
  color: #000;
  margin: 0;
  padding: 0;
}

.swiper {
  width: 100%;
  height: 100%;
}

.swiper-slide {
  text-align: center;
  font-size: 18px;
  background: #fff;

  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-button-prev {
  color: #000 !important;
}

.swiper-button-next {
  color: #000 !important;
}

.swiper-pagination-bullet-active {
  color: #000 !important;
  background-color: #000 !important;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.swiper {
  margin-left: auto;
  margin-right: auto;
}

.swiper-pagination-fraction {
  font-weight: 600 !important;
  font-style: italic !important;
}

/* Debug border */
/* *,
::before,
::after {
  box-sizing: border-box !important;
  border-width: 1px !important;
  border-style: solid !important;
  border-color: #bababb22 !important;
} */

/* react-select input focus border */
.select__input,
[type='text']:focus {
  --tw-ring-color: #cecece !important;
}

.spinner {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: radial-gradient(farthest-side, #5b3085 94%, #0000) top/8px 8px
      no-repeat,
    conic-gradient(#0000 30%, #5b3085);
  -webkit-mask: radial-gradient(farthest-side, #0000 calc(100% - 8px), #000 0);
  animation: s3 1s infinite linear;
}

@keyframes s3 {
  100% {
    transform: rotate(1turn);
  }
}

/* Debug mode - div inset */
div {
  /* box-shadow: inset 0 0 2px #0f0; */
  /* border: 1px dotted black; */
}

.rdt_Pagination svg:not(:first-child) {
  display: none !important;
}

/*  group-permission-checkbox */
.group-permission-checkbox {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border: 2px solid #55329a;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s, border-color 0.1s;
}

.group-permission-checkbox:checked {
  background-color: #55329a !important;
  border-color: black !important;
}

.group-permission-checkbox:checked::after {
  color: white;
  font-size: 16px;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

/* .group-permission-checkbox:hover {
  outline: none !important;
  box-shadow: 0 0 2px 2px rgba(85, 50, 154, 0.5) !important;
} */

.group-permission-checkbox:focus {
  outline: none !important;
  box-shadow: 0 0 0 0 rgba(85, 50, 154, 0.5) !important;
  /* box-shadow: 0 0 2px 2px rgba(85, 50, 154, 0.5) !important; */
}

.group-permission-checkbox:checked:disabled {
  background-color: #ffffff !important;
  border-color: rgb(127, 127, 127) !important;
}

.group-permission-checkbox:checked:disabled::after {
  color: white;
  font-size: 16px;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}

.react-js-cron-clear-button {
  background-color: #5b3085 !important;
  color: white !important;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
}

.react-js-cron > div {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}
