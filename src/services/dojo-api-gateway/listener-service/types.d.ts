class ListenerConfig {
  id?: string;
  name: string;
  description: string;
  type: string; // 'INTERNAL' | 'EXTERNAL';
  user: string;
  eventId: string;
  eventName?: string;
  component?: string;
  secure?: any;
  host?: any | null;
  port?: any | null;
  version?: string;
  componentSpecificProps?: ComponentSpecPropsType | null;
  componentSpecificConnPropsJson?: string | null;
  componentSpecificConnProps?: any | null;
  authenticationInfo?: AuthenticationInfoType | null;
  ipAddresses?: string[] | null;
  connectionInfo?: ConnectionInfoType | null;
  status?: 'UNKNOWN' | 'ACTIVE' | 'INACTIVE';
  createdDate?: null;
  updatedDate?: null;
}

type ConnectionInfoType = {
  componentInfo?: ComponentInfoType;
  externalAccessInfo?: ExternalAccessInfoType;
  authenticationInfo?: AuthenticationInfoType | null;
  certificateInfo?: CertificateInfoType | null;
  ipAddresses?: string[];
};

type ComponentInfoType = {
  component?: string;
  host?: string;
  port?: string;
  protocol?: string;
  secure?: boolean;
  identifier?: string;
  topic?: string;
  path?: string;
  groupId?: string;
  connectionId?: string;
  version?: string;
};

type ComponentSpecPropsType = {
  topic?: string;
  identifier?: string;
  groupId?: string;
  connectionId?: string;
};

type ExternalAccessInfoType = {
  hostname: string;
  connectionProperties: Record<string, unknown> | null;
};

type AuthenticationInfoType = {
  type?: any;
  username?: string;
  password?: string;
};

type CertificateInfoType = {
  content?: any;
  fileName?: string;
  password?: string;
  type?: string;
};

type PropertiesType = {
  IMAGE_NAME: string;
  SCOPE: string;
  VERSION: string;
  CONTAINER_ID: string;
};

type ListenerType = {
  id: string;
  name: string;
  description: string;
  hostname: string;
  eventId: string;
  eventName: string;
  component: string;
  user: string;
  status: string; // UP DOWN UNKNOWN
  createdDate: string;
  updatedDate: string;
};

type CreateListenerPayload = {
  name: string;
  description: string;
  component: string;
  hostname: string;
  eventName: string;
  eventId: string;
  user: string;
};

type EditListenerPayload = {
  name: string;
  description: string;
};

type HealthType = {
  state: string;
  id: string;
};

type ListenerComponentsType = string[];

type HealthResponseType = SuccessResponseType<HealthType>;

type ListenerResponseType = SuccessResponseType<ListenerType[]>;

type ListenerListResponseType = SuccessResponseType<ListenerType[]>;

type ListenerComponentsResponseType = SuccessResponseType<string[]>;

type DelimitersResponseType = SuccessResponseType<any>;
