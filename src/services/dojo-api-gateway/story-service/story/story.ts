import axios from 'axios';
import { instance } from '../../instance';
import { getStoryIdByName, setStoryIdByName } from './story-utils';
import { SchReport } from '@/components/reporting/SchReport.specs';
// import { SchReportsMock } from '@/components/reporting/SchReportsMock';

export const findStory = (storyName: string) =>
  instance
    .get<StoryResponseType>(`/st/story/find/${storyName}`)
    .then((data) => {
      setStoryIdByName(storyName, data.data.Data.content.id);
      return data;
    });

export const deploy = (storyKey: string): Promise<AnyResponse> =>
  instance.get(`/st/story/${storyKey}/deploy`);

export const run = (storyKey: string): Promise<AnyResponse> =>
  instance.post(`/st/story/${storyKey}/run`, {});

export const undeploy = (storyKey: string): Promise<AnyResponse> =>
  instance.post(`/st/story/${storyKey}/undeploy`, {});

export const createStory = (payload: CreateStoryPayload) =>
  instance.post<AnyResponse>('/st/story/create', payload);

export const findStoryActions = (storyKey: string) =>
  instance.get<AnyResponse>(`/st/story/?storyName=${storyKey}/actions`);

export const eventLifeCycle = (storyKey?: string, eventId?: string) =>
  instance.get<AnyResponse>(
    `/st/story/${storyKey}/${eventId}/settings/event-lifecycle`
  );

export const logsByFilter = (storyName: string, payload: any) =>
  instance.post<SuccessResponseType>(
    `/st/storyLogs/getLogs/${getStoryIdByName(storyName)}`,
    {
      ...payload,
    }
  );

export const logDetails = (storyName: string, logId: string) =>
  instance.get<SuccessResponseType>(
    `/st/storyLogs/getDetails/${getStoryIdByName(storyName)}/${logId}`
  );

export const rename = (payload: RenameStoryPayload) =>
  instance.post<StoryResponseType>('/st/story/rename', payload);

export const destroy = (storyKey: string): Promise<AnyResponse> =>
  instance.get(`/st/story/delete/${storyKey}`);

export const playerVariables = (storyKey: string) =>
  instance.get<
    SuccessResponseType<Array<{ id: string; name: string; javaType: string }>>
  >(`/st/parameter/${storyKey}/playerVariableSelect`);

export const addPlayerVariable = (payload: AddPlayerVariablePayload) =>
  instance.post(`/st/parameter/addPlayerVariable`, payload);

export const deletePlayerVariable = (payload: DeletePlayerVariablePayload) =>
  instance.post('/st/parameter/deletePlayerVariable', payload);

export const upsertRequiredFields = (payload: UpsertRequiredFieldsPayload) =>
  instance.post('/st/story/upsert-required-fields', payload);

export const getRequiredFieldsSelect = (payload: RequiredFieldsSelectList) =>
  instance.post('/st/story/getRequiredFieldsSelectList', payload);

export const editLifeCycle = (payload: EditEventLifeCyclePayload) =>
  instance.post('st/story/settings/event-lifecycle/edit', payload);

export const getStoryLogs = (storyName: string, payload?: GetLogsPayload) => {
  if (payload)
    return axios.post('http://162.222.206.223:8080/logs/getLogs', payload);
  else
    return axios.get(`http://162.222.206.223:8080/logs/getLogs/${storyName}`);
};

export const updateStateBoxBgColor = (payload: any) => {
  return instance.post<AnyResponse>(
    `/st/state/settings/background-color`,
    payload
  );
};

export const eventList = () => instance.get<ListEventTemplateResponseType>("/st/event/list-by-indexes")

export const logFields = (storyName: string) =>
  instance
    .get<SuccessResponseType>(`/st/logs/fields/${storyName}`)
    .then((data) => {
      return data;
    });

export const logQuery = (payload: any) =>
  instance.post(`/st/logs/query`, payload);

export const logDownload = (
  payload: any,
  onDownloadProgress?: (loadedMB: number, totalMB: number | null) => void
) =>
  instance.post(`/st/logs/download`, payload, {
    responseType: 'blob',
    onDownloadProgress: (progressEvent) => {
      progressEvent.total = 10000 * 1024;
      const loadedMB = +(progressEvent.loaded / (1024 * 1024)).toFixed(2);
      const totalMB = progressEvent.total
        ? +(progressEvent.total / (1024 * 1024)).toFixed(2)
        : null;

      onDownloadProgress?.(loadedMB, totalMB);
    },
  });

// export const instance1 = axios.create({
//   baseURL: 'http://localhost:3000/mock',
// });

// list
export const scheduledReports = (eventId: string) =>
  //return { data: SchReportsMock };
  instance
    .get<SuccessResponseType>(`/st/scheduled-reports?eventId=${eventId}`)
    .then((data) => {
      return data;
    });

// create
export const createSchReporting = (
  payload: SchReport
): Promise<SuccessResponseType> => {
  const filteredPayload = Object.fromEntries(
    Object.entries(payload).filter(([_, value]) => value !== null)
  ) as SchReport;
  return instance.post(`/st/scheduled-reports`, filteredPayload);
};

// update
export const updateSchReport = (
  payload: SchReport
): Promise<SuccessResponseType> => {
  const filteredPayload = Object.fromEntries(
    Object.entries(payload).filter(([_, value]) => value !== null)
  ) as SchReport;
  return instance.put(`/st/scheduled-reports/${payload.id}`, filteredPayload);
};

// toggleStatus
export const toggleSchReportStatus = (schReportId: string) =>
  instance
    .patch<SuccessResponseType>(`/st/scheduled-reports/${schReportId}/status`)
    .then((data) => {
      return data;
    });

// delete
export const deleteSchReport = (schReportId: string) => {
  return instance.delete<AnyResponse>(`/st/scheduled-reports/${schReportId}`);
};
